<template>
  <div class="device-selector">
    <div v-if="deviceList.length > 0" class="custom-select" ref="target">
      <div class="selected-option" @click="show = !show">
        <span class="text-[30px] truncate ml-[30px]">
          {{ selectedDevice ? getDeviceName(selectedDevice) : placeholder }}
        </span>
        <div class="dropdown-icon" v-if="!selectedDevice"></div>
        <div class="reset-icon" @click.stop="resetSelect" v-if="selectedDevice">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      <transition name="dropdown" v-show="show">
        <ul class="options-list">
          <li
            v-for="device in deviceList"
            :key="getDeviceId(device)"
            :class="['option', selectedId === getDeviceId(device) && 'active']"
            @click="selectDevice(device)"
          >
            <div class="device-option">
              <span>{{ getDeviceName(device) }}</span>
            </div>
          </li>
        </ul>
      </transition>
    </div>
    <div v-else class="no-device-tip">{{ emptyTip }}</div>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, watch, computed } from 'vue';
import { onClickOutside } from '@vueuse/core';

const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '选择项目类型'
  },
  idField: {
    type: String,
    default: 'deviceId'
  },
  nameField: {
    type: String,
    default: 'deviceName'
  },
  // statusField 已不再使用，保留仅为兼容调用处传参
  statusField: {
    type: String,
    default: 'deviceStatus'
  },
  emptyTip: {
    type: String,
    default: '暂无可选项'
  }
});

const emit = defineEmits(['update:modelValue', 'change', 'select']);

const { proxy } = getCurrentInstance();

const deviceList = computed(() => props.devices || []);
const selectedId = ref(props.modelValue);
const show = ref(false);
const target = ref(null);

onClickOutside(target, () => {
  show.value = false;
});

const selectedDevice = computed(() => {
  return deviceList.value.find(device => getDeviceId(device) === selectedId.value);
});

watch(() => props.modelValue, (newVal) => {
  selectedId.value = newVal;
});

watch(() => selectedId.value, (newVal) => {
  emit('update:modelValue', newVal);
  const selectedDevice = deviceList.value.find(device => getDeviceId(device) === newVal);
  emit('change', selectedDevice);
  emit('select', selectedDevice);
});

const getDeviceId = (device) => {
  return device[props.idField] || '';
};

const getDeviceName = (device) => {
  return device[props.nameField] || '未命名';
};

// 已移除状态显示

const selectDevice = (device) => {
  selectedId.value = getDeviceId(device);
  show.value = false;
};

const resetSelect = () => {
  selectedId.value = '';
  show.value = false;
};
</script>

<style lang="scss" scoped>
.device-selector {
  width: 187px !important;
  
  .custom-select {
    width: 187px !important;
    height: 75px;
    font-size: 30px;
      background-color: transparent;
      background-image: url('@/assets/images/project/选择器背景.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    box-shadow: 0px -1px 2px 0px rgba(0,0,0,0.5);
    position: relative;
    
    .selected-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 75px;
      line-height: 60px;
      cursor: pointer;
      
        .reset-icon {
          position: absolute;
          right: 30px;
        top: 50%;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        opacity: 0.7;
        transition: all 0.3s ease;
        border-radius: 50%;
        
        &:hover {
          opacity: 1;
          background: rgba(255, 255, 255, 0.1);
          color: #ff6b6b;
        }
        
        svg {
          width: 20px;
          height: 20px;
        }
      }
      
      >span {
        flex-grow: 1;
          margin-right: 110px;
        color: #fece61;
      }

        .dropdown-icon {
          position: absolute;
          right: 60px;
          top: 50%;
          transform: translateY(-50%);
          width: 30px;
          height: 30px;
          background-image: url('@/assets/images/project/选择器下拉.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          opacity: 0.9;
          pointer-events: none;
        }
    }
    
    .options-list {
      width: 260px !important;
      max-height: 375px;
      overflow-y: auto;
      position: absolute;
      top: 75px;
      left: 0;
      background: #2D4159;
      box-shadow: 0px 2px 10px 3px rgba(0,0,0,0.2), 0px 5px 10px 0px rgba(255,255,255,0.05);
      border-radius: 6px;
      border-top: none;
      padding: 10px;
      margin: 0;
      box-sizing: border-box;
      z-index: 200;
      color: #fece61;
      transition: all 0.3s ease-in-out;
    }
    
    .option {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: left;
      cursor: pointer;
      font-size: 30px;
      color: #fece61;
      box-sizing: border-box;
      padding: 12px 12px 12px 22px;
      text-align: left;
      margin-bottom: 12px;
      white-space: nowrap;
      
      .device-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
    }
    
    .option:hover {
      background: linear-gradient(0deg, #2D4159, #334A66);
      box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
      border-radius: 4px;
      color: #fece61;
      font-weight: bold;
    }
    
    .active {
      background: linear-gradient(0deg, #2D4159, #334A66);
      box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
      border-radius: 4px;
      color: #fece61;
      font-weight: bold;
    }
  }
  
  .no-device-tip {
    width: 280px !important;
    height: 75px;
    font-size: 30px;
    background: #395373;
    box-shadow: 0px -1px 2px 0px rgba(0,0,0,0.5);
    color: white;
    display: flex;
    align-items: center;
    padding: 0px 20px;
    box-sizing: border-box;
  }
}

/* 自定义滚动条样式 */
.options-list::-webkit-scrollbar {
  width: 6px;
}

.options-list::-webkit-scrollbar-thumb {
  background-color: #33BBFF;
  border-radius: 3px;
  box-shadow: 1px 0px 2px 0px rgba(255,255,255,0.5);
}

.options-list::-webkit-scrollbar-track {
  background: #27384D;
  box-shadow: 1px 0px 2px 0px rgba(0,0,0,0.3);
  border-radius: 3px;
  box-sizing: border-box;
  padding: 10px 0;
}

/* 响应式调整 */
@media (min-aspect-ratio: 16/9) {
  .device-selector {
    width: 23.333rem !important;
    
    .custom-select {
      width: 23.333rem !important;
      
      .selected-option {
        >span {
          margin-right: 140px;
        }
        
        .reset-icon {
          right: 70px;
          width: 35px;
          height: 35px;
          
          svg {
            width: 24px;
            height: 24px;
          }
        }

        .dropdown-icon {
          right: 75px;
          width: 35px;
          height: 35px;
        }
      }
      
      .options-list {
        width: 23.333rem !important;
      }
    }
    
    .no-device-tip {
      width: 23.333rem !important;
    }
  }
}

/* 下拉动画 */
.dropdown-enter-active, .dropdown-leave-active {
  transition: all 0.3s ease-in-out;
}

.dropdown-enter-from, .dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>



